'use client';

import Link from 'next/link';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaYoutube, FaTiktok } from 'react-icons/fa';
import { motion } from 'framer-motion';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    { name: "YouTube", href: "#", icon: <FaYoutube size={24} /> },
    { name: "Instagram", href: "#", icon: <FaInstagram size={24} /> },
    { name: "TikTok", href: "#", icon: <FaTiktok size={24} /> },
    { name: "X (Twitter)", href: "#", icon: <FaTwitter size={24} /> },
  ];

  const footerLinks = [
    { href: "/privacy-policy", label: "Privacy Policy" },
    { href: "/terms-of-service", label: "Terms of Service" },
    { href: "/contact", label: "Contact Us" },
  ];



  return (
    <motion.footer
      className="bg-surface text-text-secondary py-12 border-t border-border"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
          {/* Logo and Copyright */}
          <motion.div
            className="text-center md:text-left"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <motion.div
              whileHover={{ scale: 1.05, color: "var(--brand-yellow)" }}
              transition={{ duration: 0.3 }}
            >
              <Link href="/" className="text-2xl font-bold text-text-primary transition-colors duration-300">
                Jackson Abetianbe
              </Link>
            </motion.div>
            <p className="mt-2 text-sm">
              &copy; {currentYear} Jackson Abetianbe. All rights reserved.
            </p>
            <p className="text-xs mt-1">Crafting visual stories that captivate.</p>
          </motion.div>

          {/* Footer Links */}
          <motion.nav
            className="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {footerLinks.map((link, index) => (
              <motion.div
                key={link.href}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05, color: "var(--brand-yellow)" }}
              >
                <Link
                  href={link.href}
                  className="hover:text-brand-yellow transition-colors duration-300 text-sm"
                >
                  {link.label}
                </Link>
              </motion.div>
            ))}
          </motion.nav>

          {/* Social Media Links */}
          <motion.div
            className="flex justify-center md:justify-end space-x-5"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            {socialLinks.map((social, index) => (
              <motion.a
                key={social.name}
                href={social.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={`Follow us on ${social.name}`}
                className="text-text-secondary transition-colors duration-300"
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  color: "var(--brand-yellow)",
                  scale: 1.2
                }}
                whileTap={{ scale: 0.9 }}
              >
                {social.icon}
              </motion.a>
            ))}
          </motion.div>
        </div>

        {/* Optional: Back to top button or other elements */}
        <motion.div
          className="mt-8 pt-8 border-t border-border/50 text-center text-xs"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <p>Designed with passion by Jackson Abetianbe</p>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;