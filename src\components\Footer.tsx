'use client';

import Link from 'next/link';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaYoutube, FaTiktok } from 'react-icons/fa';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    { name: "YouTube", href: "#", icon: <FaYoutube size={24} /> },
    { name: "Instagram", href: "#", icon: <FaInstagram size={24} /> },
    { name: "TikTok", href: "#", icon: <FaTiktok size={24} /> },
    { name: "X (Twitter)", href: "#", icon: <FaTwitter size={24} /> },
  ];

  const footerLinks = [
    { href: "/privacy-policy", label: "Privacy Policy" },
    { href: "/terms-of-service", label: "Terms of Service" },
    { href: "/contact", label: "Contact Us" },
  ];

  // GSAP hover effect for social icons
  useEffect(() => {
    const socialIconElements = document.querySelectorAll(".social-icon-link");
    socialIconElements.forEach((icon) => {
      const originalColor = getComputedStyle(icon).color;
      icon.addEventListener("mouseenter", () => {
        gsap.to(icon, { color: "var(--brand-yellow)", scale: 1.2, duration: 0.3, ease: "power2.out" });
      });
      icon.addEventListener("mouseleave", () => {
        gsap.to(icon, { color: originalColor, scale: 1, duration: 0.3, ease: "power2.out" });
      });
    });
  }, []);

  return (
    <footer className="bg-surface text-text-secondary py-12 border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
          {/* Logo and Copyright */}
          <div className="text-center md:text-left">
            <Link href="/" className="text-2xl font-bold text-text-primary hover:text-brand-yellow transition-colors duration-300">
              Jackson Abetianbe
            </Link>
            <p className="mt-2 text-sm">
              &copy; {currentYear} Jackson Abetianbe. All rights reserved.
            </p>
            <p className="text-xs mt-1">Crafting visual stories that captivate.</p>
          </div>

          {/* Footer Links */}
          <nav className="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-6">
            {footerLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="hover:text-brand-yellow transition-colors duration-300 text-sm"
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Social Media Links */}
          <div className="flex justify-center md:justify-end space-x-5">
            {socialLinks.map((social) => (
              <a
                key={social.name}
                href={social.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={`Follow us on ${social.name}`}
                className="social-icon-link text-text-secondary transition-colors duration-300"
                style={{ willChange: 'color, transform' }} // Performance hint for GSAP
              >
                {social.icon}
              </a>
            ))}
          </div>
        </div>

        {/* Optional: Back to top button or other elements */}
        <div className="mt-8 pt-8 border-t border-border/50 text-center text-xs">
          <p>Designed with passion by Jackson Abetianbe</p> 
          {/* Or if a different designer: <p>Website by YourAgencyName</p> */}
        </div>
      </div>
    </footer>
  );
};

export default Footer;