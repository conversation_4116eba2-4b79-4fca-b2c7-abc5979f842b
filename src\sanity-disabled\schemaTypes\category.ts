import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'category',
  title: 'Category',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
    }),
    defineField({
      name: 'editingApproach',
      title: 'Editing Approach',
      type: 'text',
      description: 'Describe the editing philosophy/approach for this category.'
    }),
    defineField({
      name: 'techniques',
      title: 'Key Techniques & Specialties',
      type: 'array',
      of: [{type: 'string'}],
      description: 'List key techniques and specialties for this category.'
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'description',
    },
  },
})