"use client";

import Link from 'next/link';
import Image from 'next/image';
import { FaChevronDown, FaPlayCircle, FaEnvelope, FaStar, FaVideo, FaAward } from 'react-icons/fa';
import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';

export default function Home() {
  const heroRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  const contentInView = useInView(contentRef, { once: true, margin: "-100px" });
  const statsInView = useInView(statsRef, { once: true, margin: "-100px" });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 80, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 1,
        ease: "easeOut"
      }
    }
  };

  const statsVariants = {
    hidden: { scale: 0, rotate: 180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: 0.8,
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <div className="bg-background text-text-primary overflow-hidden">
      {/* Hero Section */}
      <section ref={heroRef} className="min-h-screen flex flex-col justify-center items-center text-center p-8 relative">
        {/* Animated background particles */}
        <div className="absolute inset-0 bg-black">
          <motion.div
            className="absolute top-1/4 left-1/4 w-2 h-2 bg-brand-purple rounded-full opacity-50"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
          <motion.div
            className="absolute top-3/4 right-1/4 w-1 h-1 bg-brand-yellow rounded-full opacity-70"
            animate={{ scale: [1, 1.5, 1], opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 1, repeat: Infinity }}
          />
          <motion.div
            className="absolute top-1/2 left-3/4 w-3 h-3 bg-brand-purple rounded-full opacity-40"
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </div>

        <div className="relative z-10 flex flex-col items-center">
          <motion.div
            className="mb-8"
            initial={{ scale: 0, rotate: -180, opacity: 0 }}
            animate={{ scale: 1, rotate: 0, opacity: 1, y: [-10, 0, -10] }}
            transition={{
              scale: { duration: 1.2, type: "spring", stiffness: 100 },
              rotate: { duration: 1.2, type: "spring", stiffness: 100 },
              opacity: { duration: 1.2 },
              y: { duration: 2, repeat: Infinity, ease: "easeInOut", delay: 1.5 }
            }}
          >
            <Image
              src="/placeholder-headshot.svg"
              alt="Video Editor Headshot"
              width={128}
              height={128}
              className="rounded-full border-4 border-brand-purple shadow-2xl hover:border-brand-yellow transition-colors duration-300"
            />
          </motion.div>

          <motion.h1
            className="text-5xl md:text-7xl font-bold mb-4 bg-gradient-to-r from-white via-brand-purple to-brand-yellow bg-clip-text text-transparent"
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            <motion.span
              className="inline-block"
              initial={{ y: 100, opacity: 0, rotateX: 90 }}
              animate={{ y: 0, opacity: 1, rotateX: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              Masterful
            </motion.span>{' '}
            <motion.span
              className="inline-block"
              initial={{ y: 100, opacity: 0, rotateX: 90 }}
              animate={{ y: 0, opacity: 1, rotateX: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              Video
            </motion.span>{' '}
            <motion.span
              className="inline-block text-brand-purple"
              initial={{ y: 100, opacity: 0, rotateX: 90 }}
              animate={{ y: 0, opacity: 1, rotateX: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              Editing
            </motion.span>
          </motion.h1>

          <motion.h2
            className="text-5xl md:text-7xl font-bold mb-6"
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: "auto", opacity: 1 }}
            transition={{ duration: 1, delay: 1.2 }}
          >
            for Impact
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-text-secondary mb-10 max-w-xl leading-relaxed"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.5 }}
          >
            I transform raw footage into polished, compelling narratives that captivate your audience and elevate your brand. Your vision, expertly edited.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12"
            initial={{ y: 30, opacity: 0, scale: 0.8 }}
            animate={{ y: 0, opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 1.8, type: "spring", stiffness: 100 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/portfolio" className="group bg-gradient-to-r from-brand-purple to-purple-700 hover:from-purple-700 hover:to-brand-purple text-text-on-purple font-semibold py-3 px-8 rounded-md text-lg flex items-center justify-center w-full sm:w-auto shadow-lg hover:shadow-xl transition-all duration-300">
                <FaPlayCircle className="mr-2 group-hover:animate-spin" /> View My Work
              </Link>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ y: 30, opacity: 0, scale: 0.8 }}
              animate={{ y: 0, opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 2.0, type: "spring", stiffness: 100 }}
            >
              <Link href="/contact" className="group border-2 border-brand-purple hover:bg-gradient-to-r hover:from-brand-purple hover:to-purple-700 hover:border-transparent text-text-primary font-semibold py-3 px-8 rounded-md text-lg flex items-center justify-center w-full sm:w-auto transition-all duration-300">
                <FaEnvelope className="mr-2 group-hover:animate-pulse" /> Get In Touch
              </Link>
            </motion.div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: [0, 10, 0], opacity: 1 }}
            transition={{
              y: { duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 2.3 },
              opacity: { duration: 0.5, delay: 2.3 }
            }}
          >
            <FaChevronDown className="text-3xl text-text-muted cursor-pointer hover:text-brand-yellow transition-colors duration-300" />
          </motion.div>
        </div>
      </section>

      {/* Enhanced Content Section */}
      <section className="py-20 bg-gradient-to-b from-surface-dark to-background text-center relative">
        <div className="container mx-auto px-6">
          <motion.div
            ref={contentRef}
            variants={containerVariants}
            initial="hidden"
            animate={contentInView ? "visible" : "hidden"}
          >
            <motion.h2
              className="text-4xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-brand-yellow to-brand-purple bg-clip-text text-transparent"
              variants={itemVariants}
            >
              Why Choose Jackson Abetianbe?
            </motion.h2>
            <motion.p
              className="text-lg md:text-xl text-text-muted mb-16 max-w-3xl mx-auto leading-relaxed"
              variants={itemVariants}
            >
              Experience the difference of professional video editing that brings your vision to life with cinematic quality and creative excellence.
            </motion.p>

            {/* Stats Section */}
            <motion.div
              ref={statsRef}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
              variants={containerVariants}
              initial="hidden"
              animate={statsInView ? "visible" : "hidden"}
            >
              {[
                { icon: FaVideo, number: "500+", label: "Projects Completed", color: "brand-purple" },
                { icon: FaStar, number: "5.0", label: "Average Rating", color: "brand-yellow" },
                { icon: FaAward, number: "50+", label: "Awards Won", color: "brand-purple" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-br from-surface to-surface-dark p-8 rounded-xl shadow-2xl"
                  variants={statsVariants}
                  whileHover={{
                    y: -8,
                    boxShadow: stat.color === "brand-yellow"
                      ? "0 25px 50px -12px rgba(255, 215, 0, 0.2)"
                      : "0 25px 50px -12px rgba(138, 43, 226, 0.2)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <stat.icon className={`text-4xl text-${stat.color} mb-4 mx-auto`} />
                  <h3 className="text-3xl font-bold text-text-primary mb-2">{stat.number}</h3>
                  <p className="text-text-muted">{stat.label}</p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Floating background elements */}
        <motion.div
          className="absolute top-10 left-10 w-20 h-20 bg-brand-purple/10 rounded-full blur-xl"
          animate={{ opacity: [0.3, 0.8, 0.3] }}
          transition={{ duration: 3, repeat: Infinity }}
        />
        <motion.div
          className="absolute bottom-10 right-10 w-32 h-32 bg-brand-yellow/10 rounded-full blur-xl"
          animate={{ opacity: [0.3, 0.8, 0.3] }}
          transition={{ duration: 4, repeat: Infinity }}
        />
      </section>
    </div>
  );
}
