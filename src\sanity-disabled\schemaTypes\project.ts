import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'project',
  title: 'Project',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
    }),
    defineField({
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [{type: 'category'}],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'videoUrl',
      title: 'Video URL',
      type: 'url',
      description: 'URL of the video (e.g., YouTube or Vimeo)',
      validation: (Rule) => Rule.required().uri({
        scheme: ['http', 'https']
      })
    }),
    defineField({
      name: 'thumbnail',
      title: 'Thumbnail',
      type: 'image',
      options: {
        hotspot: true, // Enables image cropping
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{type: 'string'}],
      options: {
        layout: 'tags',
      },
    }),
    defineField({
      name: 'date',
      title: 'Date',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
     defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
        name: 'client',
        title: 'Client',
        type: 'string',
    }),
    defineField({
        name: 'projectBrief',
        title: 'Project Brief',
        type: 'text',
    }),
    defineField({
        name: 'challenges',
        title: 'Challenges',
        type: 'text',
    }),
    defineField({
        name: 'solutions',
        title: 'Solutions',
        type: 'text',
    }),
    defineField({
        name: 'technicalDetails',
        title: 'Technical Details',
        type: 'text',
    }),
    defineField({
        name: 'creativeDecisions',
        title: 'Creative Decisions',
        type: 'text',
    }),
  ],
  preview: {
    select: {
      title: 'title',
      media: 'thumbnail',
      category: 'category.name',
    },
    prepare(selection) {
      const {title, media, category} = selection
      return {
        title: title,
        subtitle: category ? `Category: ${category}` : 'No category',
        media: media,
      }
    },
  },
})