{"name": "jaeylo-video-editing-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "babel-plugin-react-compiler": "^19.1.0-rc.2", "gsap": "^3.12.5", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.4.0", "three": "^0.177.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}