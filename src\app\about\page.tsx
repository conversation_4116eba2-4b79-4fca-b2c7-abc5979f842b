import Image from 'next/image';
import Link from 'next/link';
import { FaPalette, FaHeadphones, FaLaptop, FaDesktop, FaPencilAlt, FaFilm, FaBoxOpen, FaCheckDouble } from 'react-icons/fa';

// Main page component
export default function AboutPage() {
  return (
    <div className="bg-background text-text-primary min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
        {/* Hero Section */}
        <section className="flex flex-col md:flex-row items-center md:items-start py-8 md:py-12">
          <div className="md:w-1/3 flex justify-center md:justify-start mb-8 md:mb-0 md:pr-8">
            <Image
              src="/placeholder-headshot.svg" // Replace with actual headshot
              alt="Jackson Abetianbe Headshot"
              width={150}
              height={180}
              className="rounded-full object-cover shadow-xl border-4 border-border"
            />
          </div>
          <div className="md:w-2/3 text-center md:text-left">
            <h1 className="text-4xl lg:text-5xl font-bold mb-3 text-text-primary">Jackson Abetianbe</h1>
            <p className="text-xl text-brand-yellow mb-6 font-semibold">Lead Video Editor & Creative Director</p>
            <p className="text-text-secondary mb-6 leading-relaxed">
              Expert video editor specializing in <span className="text-brand-yellow font-medium">cinematic storytelling</span> and high-end visual content. 
              With a passion for transforming raw footage into compelling narratives, Jackson brings a meticulous eye for detail and a creative flair to every project. 
              From corporate videos to music videos, and short films to social media content, the goal is always to exceed expectations and deliver impactful results.
            </p>
            <p className="text-sm text-text-muted">Based in Los Angeles, CA</p>
          </div>
        </section>

        {/* About Me Section */}
        <section className="my-12 md:my-16">
          <h2 className="text-3xl font-semibold mb-6 text-text-primary">About Me</h2>
          <p className="text-text-secondary leading-relaxed text-justify">
            With over a decade of experience in video editing, I've honed my craft to deliver exceptional visual experiences. My passion lies in transforming raw footage into compelling narratives that resonate with audiences. I approach each project with meticulous attention to detail, ensuring every frame contributes to the overall vision. My work focuses on artistic merit and technical excellence in editing.
          </p>
        </section>

        {/* Services Section */}
        <section className="my-12 md:my-16">
          <h2 className="text-3xl font-semibold mb-8 text-text-primary">Services</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <ServiceCard
              imageUrl="/service-narrative.svg" // Placeholder: Add actual image to /public
              title="Narrative & Documentary Editing"
              description="Crafting compelling stories through precise editing for films and documentaries."
              price="Starting at $2,500"
            />
            <ServiceCard
              imageUrl="/service-promotional.svg" // Placeholder: Add actual image to /public
              title="Promotional & Commercial Editing"
              description="Creating impactful and engaging video content for brands and businesses."
              price="Starting at $1,500"
            />
            <ServiceCard
              imageUrl="/service-social.svg" // Placeholder: Add actual image to /public
              title="Social Media & Digital Content Editing"
              description="Editing short-form content optimized for social media platforms and online campaigns."
              price="Starting at $800"
            />
          </div>
        </section>

        {/* Editing Process Section */}
        <section className="my-12 md:my-16">
          <h2 className="text-3xl font-semibold mb-8 text-text-primary">Editing Process</h2>
          <div className="space-y-6">
            <ProcessStep icon={<FaPencilAlt />} title="Concept Development" description="Understanding your vision, goals, and target audience to lay a strong foundation." />
            <ProcessStep icon={<FaFilm />} title="Editing & Refinement" description="Crafting the narrative, pacing, and flow, then refining the edit for maximum impact." />
            <ProcessStep icon={<FaPalette />} title="Color Grading & Sound Design" description="Enhancing visuals with professional color correction and creating an immersive audio experience." />
            <ProcessStep icon={<FaCheckDouble />} title="Final Delivery" description="Delivering the polished final product in your desired format, optimized for its intended platform." />
          </div>
        </section>

        {/* Software & Equipment Section */}
        <section className="my-12 md:my-16">
          <h2 className="text-3xl font-semibold mb-8 text-text-primary">Software & Equipment</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <EquipmentCard icon={<FaLaptop />} title="Editing Software" description="Adobe Premiere Pro, DaVinci Resolve" />
            <EquipmentCard icon={<FaDesktop />} title="High-Performance Workstation" description="Custom-built PC for demanding editing tasks" />
            <EquipmentCard icon={<FaHeadphones />} title="Professional Audio Gear" description="Studio Headphones, Audio Interface" />
            <EquipmentCard icon={<FaPalette />} title="Color Accurate Monitors" description="Calibrated Displays for precise color grading" />
          </div>
        </section>

        {/* Personal Touch Section */}
        <section className="my-12 md:my-16">
          <h2 className="text-3xl font-semibold mb-6 text-text-primary">Personal Touch</h2>
          <p className="text-text-secondary leading-relaxed text-justify">
            Beyond the technical aspects of video editing, I draw inspiration from various sources, including classic cinema, photography, and the vibrant culture of Paris. My creative process involves a blend of meticulous planning and intuitive experimentation, allowing me to discover unique and compelling ways to tell stories. In my free time, I enjoy exploring the city's hidden gems, attending film screenings, and capturing moments through photography. This diverse range of interests infuses my work with a fresh perspective and a unique artistic sensibility.
          </p>
        </section>

        {/* Call to Action Section */}
        <section className="my-12 md:my-16 text-center">
          <div className="bg-gradient-to-r from-brand-purple to-purple-700 rounded-lg p-8 shadow-xl">
            <h2 className="text-3xl font-semibold mb-4 text-text-on-purple">Ready to Create Something Amazing?</h2>
            <p className="text-text-on-purple mb-6 text-lg">
              Let's collaborate to bring your vision to life with professional video editing that exceeds expectations.
            </p>
            <Link href="/contact">
              <button className="bg-brand-yellow hover:bg-yellow-500 text-background font-bold py-3 px-8 rounded-lg text-lg transition-colors shadow-lg hover:shadow-xl">
                Let's Create Together
              </button>
            </Link>
          </div>
        </section>
      </div>
    </div>
  );
}

// Helper Components
const ServiceCard = ({ imageUrl, title, description, price }: { imageUrl: string, title: string, description: string, price: string }) => (
  <div className="bg-surface rounded-lg shadow-xl overflow-hidden flex flex-col transform hover:scale-105 transition-transform duration-300">
    <div className="relative w-full h-48">
      <Image 
        src={imageUrl} 
        alt={title}
        fill
        style={{ objectFit: 'cover' }}
        className="transition-opacity duration-300 group-hover:opacity-90"
      />
    </div>
    <div className="p-6 flex flex-col flex-grow">
      <h3 className="text-xl font-semibold mb-2 text-text-primary">{title}</h3>
      <p className="text-text-secondary mb-4 text-sm flex-grow">{description}</p>
      <button className="mt-auto bg-brand-purple hover:bg-purple-700 text-text-on-purple font-bold py-2 px-4 rounded-md text-sm transition-colors w-full sm:w-auto self-start shadow-md hover:shadow-lg">
        {price}
      </button>
    </div>
  </div>
);

const ProcessStep = ({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) => (
  <div className="flex items-start bg-surface p-5 rounded-lg shadow-lg">
    <div className="text-brand-yellow text-2xl mr-5 mt-1 flex-shrink-0 bg-background p-3 rounded-full shadow-md">
      {icon}
    </div>
    <div>
      <h3 className="text-lg font-semibold mb-1 text-text-primary">{title}</h3>
      <p className="text-text-secondary text-sm">{description}</p>
    </div>
  </div>
);

const EquipmentCard = ({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) => (
  <div className="bg-surface rounded-lg p-6 shadow-lg flex items-center transform hover:scale-105 transition-transform duration-300">
    <div className="text-brand-purple text-3xl mr-4 bg-background p-3 rounded-lg shadow-md">
      {icon}
    </div>
    <div>
      <h3 className="text-lg font-semibold mb-1 text-text-primary">{title}</h3>
      <p className="text-text-secondary text-sm">{description}</p>
    </div>
  </div>
);
