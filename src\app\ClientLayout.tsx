'use client';

import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import dynamic from 'next/dynamic';
import ErrorBoundary from '../components/ErrorBoundary';

// Dynamically import ThreeDElement for better performance
const ThreeDElement = dynamic(() => import('../components/ThreeDElement'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gradient-to-br from-purple-900/10 to-black/10 animate-pulse" />
  )
});

interface ClientLayoutProps {
  children: React.ReactNode;
}

const ClientLayout = ({ children }: ClientLayoutProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [show3D, setShow3D] = useState(false);
  const pathname = usePathname();
  const prevPathnameRef = useRef(pathname);

  // Delay 3D element loading for better initial performance
  useEffect(() => {
    const timer = setTimeout(() => setShow3D(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  // Initial loading animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  // Page transition animation
  useEffect(() => {
    if (prevPathnameRef.current !== pathname && !isLoading) {
      setIsTransitioning(true);

      const timer = setTimeout(() => {
        setIsTransitioning(false);
        prevPathnameRef.current = pathname;
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [pathname, isLoading]);

  // Custom cursor effects
  useEffect(() => {
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.style.cssText = `
      position: fixed;
      width: 20px;
      height: 20px;
      background: linear-gradient(45deg, #800080, #FFD700);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      mix-blend-mode: difference;
      transition: transform 0.1s ease, scale 0.3s ease;
    `;
    document.body.appendChild(cursor);

    const cursorFollower = document.createElement('div');
    cursorFollower.className = 'cursor-follower';
    cursorFollower.style.cssText = `
      position: fixed;
      width: 40px;
      height: 40px;
      border: 2px solid rgba(255, 215, 0, 0.3);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9998;
      transition: transform 0.2s ease, scale 0.3s ease;
    `;
    document.body.appendChild(cursorFollower);

    const handleMouseMove = (e: MouseEvent) => {
      cursor.style.transform = `translate3d(${e.clientX - 10}px, ${e.clientY - 10}px, 0)`;
      requestAnimationFrame(() => {
        cursorFollower.style.transform = `translate3d(${e.clientX - 20}px, ${e.clientY - 20}px, 0)`;
      });
    };

    const handleMouseEnter = () => {
      cursor.style.transform += ' scale(1.5)';
      cursorFollower.style.transform += ' scale(1.5)';
    };

    const handleMouseLeave = () => {
      cursor.style.transform = cursor.style.transform.replace(' scale(1.5)', '');
      cursorFollower.style.transform = cursorFollower.style.transform.replace(' scale(1.5)', '');
    };

    document.addEventListener('mousemove', handleMouseMove);

    const interactiveElements = document.querySelectorAll('a, button, [role="button"]');
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
      cursor.remove();
      cursorFollower.remove();
    };
  }, []);

  return (
    <>
      {/* 3D Element Background */}
      <AnimatePresence>
        {show3D && (
          <motion.div
            className="fixed inset-0 z-0 opacity-20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.2 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1 }}
          >
            <ErrorBoundary>
              <ThreeDElement />
            </ErrorBoundary>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Loading Screen */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            className="fixed inset-0 z-50 bg-gradient-to-br from-black via-purple-900 to-black flex flex-col items-center justify-center"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="text-6xl font-bold bg-gradient-to-r from-brand-yellow to-yellow-300 bg-clip-text text-transparent mb-8"
              initial={{ scale: 0, rotate: -180, opacity: 0 }}
              animate={{ scale: 1, rotate: 0, opacity: 1 }}
              transition={{ duration: 1, type: "spring", stiffness: 100 }}
            >
              Jackson Abetianbe Video Editing
            </motion.div>
            <motion.div
              className="text-xl text-text-secondary mb-8"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              Crafting Visual Excellence
            </motion.div>
            <div className="w-64 h-1 bg-surface-dark rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-brand-purple to-brand-yellow rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 1.5, delay: 0.8 }}
                style={{ transformOrigin: 'left' }}
              />
            </div>
            <div className="mt-8 flex space-x-2">
              {[0, 150, 300].map((delay, index) => (
                <motion.div
                  key={index}
                  className={`w-2 h-2 ${index % 2 === 0 ? 'bg-brand-yellow' : 'bg-brand-purple'} rounded-full`}
                  animate={{ y: [0, -10, 0] }}
                  transition={{
                    duration: 0.6,
                    repeat: Infinity,
                    delay: delay / 1000,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Page Transition Curtain */}
      <AnimatePresence>
        {isTransitioning && (
          <motion.div
            className="fixed inset-0 z-40 bg-gradient-to-br from-brand-purple to-black"
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            exit={{ scaleY: 0 }}
            transition={{ duration: 0.4 }}
            style={{ transformOrigin: 'top' }}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                className="text-4xl font-bold bg-gradient-to-r from-brand-yellow to-yellow-300 bg-clip-text text-transparent"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                Loading...
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content Container */}
      <motion.div
        ref={containerRef}
        className="relative z-10"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: isLoading ? 0 : 1, y: isLoading ? 30 : 0 }}
        transition={{ duration: 0.8, delay: isLoading ? 0 : 0.5 }}
      >
        {children}
      </motion.div>

      {/* Background Particles */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <motion.div
          className="absolute top-1/4 left-1/4 w-1 h-1 bg-brand-yellow rounded-full opacity-30"
          animate={{ opacity: [0.3, 0.8, 0.3] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
        <motion.div
          className="absolute top-3/4 right-1/4 w-0.5 h-0.5 bg-brand-purple rounded-full opacity-40"
          animate={{ scale: [1, 1.5, 1], opacity: [0.4, 1, 0.4] }}
          transition={{ duration: 1, repeat: Infinity }}
        />
        <motion.div
          className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-brand-yellow rounded-full opacity-20"
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/2 w-0.5 h-0.5 bg-brand-purple rounded-full opacity-50"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        />
      </div>
    </>
  );
};

export default ClientLayout;